/**
 * 优惠梯度叠加
 * 根据Shopline Storefront API - productDiscounts 获取到商品自动折扣活动信息
 * 在商品已有的折扣基础上叠加，并更新在页面：商品详情页、搜索结果页、分类商品列表页、首页商品列表组件处的商品价格、折扣、原价信息
 *
 * 使用 MutationObserver 监听页面上 card__badge 元素的变化，自动处理商品折扣信息的更新
 */

// 全局商品信息存储
const productInfoMap = new Map();

// 全局折扣信息存储
let globalDiscountData = null;

// 配置对象
const CONFIG = {
  // API配置
  API_URL: "https://caguuu.myshopline.com/storefront/graph/v20250601/graphql.json",
  API_TOKEN: "Bearer eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJ5c291bCIsInRva2VuIjoiZGFlNWZmZTk0OGNiMjIwNGYzYzNjNTlkZjgwMGIzY2QzYWUyMGU1Y3w4ODUzNXwxNzI2NDY0MTkyNDI3fDE3NDY3NzE2NjB8MjA2MTU1Nzk2MnwifQ.eYX30QQX4a-hCvA8u_yhfMLzsCvn7DwzkFvLPeZZkSu05HlwuUTA4vXJ2DHpuDinVbLvM-mHeGgV0tUcbXW8Kg",

  // DOM选择器
  SELECTORS: {
    BADGE: '.card__badge',
    PRODUCT_CARD: '[data-product-id]',
    PRICE_SALE: '.price-item--sale',
    PRICE_REGULAR: '.price-item--regular',
    PRICE_CONTAINER: '.price__container'
  },

  // 货币格式化
  CURRENCY: {
    SYMBOL: '¥',
    LOCALE: 'ja-JP'
  }
};

/**
 * 获取商品折扣信息
 * @param {string} productId - 商品ID
 * @returns {Promise<Object>} 折扣信息对象
 */
async function getBasicProductDiscounts(productId) {
  const headers = {
    'content-type': 'application/json',
    'authorization': CONFIG.API_TOKEN
  };

  const body = {
    query: `query GetBasicProductDiscounts($p1: ProductDiscountsInput!) {
  p1: productDiscounts(productDiscountsInput: $p1) {
    productId
    presentCurrency
    buyerId
    autoDiscountActivities {
      activityName
      activitySeq
      discountStyleText
      benefitConditions {
        benefit {
          benefitCount
          benefitAmount
          benefitValueType
          discount
          fixedPrice
          offPercent
          fixedPrice
          promotionSeq
        }
        benefitEvent {
          isAccumulated
          minThreshold
        }
      }
      benefitScopeType
    }
  }
}`,
    variables: {
      p1: {
        discountsProduct: {
          productId: `gid://shopline/Product/${productId}`,
        },
      },
    },
    operationName: "GetBasicProductDiscounts",
  };

  try {
    const response = await fetch(CONFIG.API_URL, {
      method: "POST",
      headers: headers,
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    const extractedData = extractDiscountData(data);
    return extractedData;
  } catch (error) {
    console.error("Error fetching product discounts:", error);

    return {
      productId,
      discounts: [],
      summary: {
        totalActivities: 0,
        bestDiscount: 0
      },
      error: error.message
    };
  }
}

function extractDiscountData(responseData) {
  const p1 = responseData.data?.p1;
  if (!p1 || !p1.autoDiscountActivities) {
    return { discounts: [], productId: null };
  }

  const productId = p1.productId;
  const activities = p1.autoDiscountActivities;

  const extractedDiscounts = activities.map(activity => {
    const discountTiers = activity.benefitConditions.map(condition => ({
      minThreshold: parseInt(condition.benefitEvent.minThreshold),
      minThresholdText: parseInt(condition.benefitEvent.minThreshold) / 100,
      discount: condition.benefit.discount,
      discountText: `${100 - condition.benefit.discount}% OFF`
    })).sort((a, b) => a.minThreshold - b.minThreshold);

    return {
      activityName: activity.activityName,
      activitySeq: activity.activitySeq,
      discountStyleText: activity.discountStyleText,
      discountTiers: discountTiers
    };
  });

  return {
    productId,
    discounts: extractedDiscounts,
    summary: {
      totalActivities: activities.length,
      bestDiscount: Math.max(...activities.flatMap(a =>
        a.benefitConditions.map(c => c.benefit.discount)
      ))
    }
  };
}

/**
 * 从商品卡片DOM中解析商品信息
 * @param {Element} productCard - 包含data-product-id的商品卡片元素
 * @returns {Object} 商品信息对象
 */
function parseProductInfoFromDOM(productCard) {
  const productId = productCard.getAttribute('data-product-id');
  if (!productId) {
    console.warn('Product card missing data-product-id attribute');
    return null;
  }

  // 获取商品标题
  const titleElement = productCard.querySelector('.product__title');
  const title = titleElement ? titleElement.textContent.trim() : '';

  // 获取价格信息
  const priceContainer = productCard.querySelector(CONFIG.SELECTORS.PRICE_CONTAINER);
  let originalPrice = 0;
  let salePrice = 0;
  let currentDiscount = 0;

  if (priceContainer) {
    // 解析售价
    const salePriceElement = priceContainer.querySelector(CONFIG.SELECTORS.PRICE_SALE);
    if (salePriceElement) {
      // 提取数字，支持逗号分隔的千位数
      const salePriceText = salePriceElement.textContent.replace(/[^\d,]/g, '').replace(/,/g, '');
      salePrice = parseInt(salePriceText) || 0;
    }

    // 解析原价
    const regularPriceElement = priceContainer.querySelector(CONFIG.SELECTORS.PRICE_REGULAR);
    if (regularPriceElement) {
      // 提取数字，支持逗号分隔的千位数
      const regularPriceText = regularPriceElement.textContent.replace(/[^\d,]/g, '').replace(/,/g, '');
      originalPrice = parseInt(regularPriceText) || 0;
    }
  }

  // 如果没有原价，使用售价作为原价
  if (originalPrice === 0 && salePrice > 0) {
    originalPrice = salePrice;
  }

  // 计算当前折扣率
  if (originalPrice > 0 && salePrice > 0 && salePrice < originalPrice) {
    currentDiscount = Math.round((1 - salePrice / originalPrice) * 100);
  }

  // 获取折扣徽章信息
  const badgeElement = productCard.querySelector(CONFIG.SELECTORS.BADGE);
  let badgeText = '';
  if (badgeElement) {
    badgeText = badgeElement.textContent.trim();
  }

  return {
    productId,
    title,
    originalPrice,
    salePrice,
    currentDiscount,
    badgeText,
    domElements: {
      productCard,
      priceContainer,
      badgeElement,
      salePriceElement: priceContainer?.querySelector(CONFIG.SELECTORS.PRICE_SALE),
      regularPriceElement: priceContainer?.querySelector(CONFIG.SELECTORS.PRICE_REGULAR)
    }
  };
}

/**
 * 计算应用折扣后的价格
 * @param {Object} productInfo - 商品信息
 * @param {Object} discountData - 折扣数据
 * @returns {Object} 计算后的价格信息
 */
function calculateDiscountedPrices(productInfo, discountData) {
  if (!discountData.discounts || discountData.discounts.length === 0) {
    return {
      finalSalePrice: productInfo.salePrice,
      finalOriginalPrice: productInfo.originalPrice,
      finalDiscount: productInfo.currentDiscount,
      appliedDiscounts: [],
      hasApiDiscount: false
    };
  }

  let bestApiDiscount = 0;
  let appliedDiscounts = [];
  let appliedTier = null;

  // 获取商品价格（日元）
  const productPrice = productInfo.salePrice;

  // 找到适用于当前商品价格的最佳折扣
  discountData.discounts.forEach(discount => {
    // 按照门槛从高到低排序折扣梯度
    const sortedTiers = [...discount.discountTiers].sort((a, b) => b.minThreshold - a.minThreshold);
    console.log('sortedTiers: ', sortedTiers);
    console.log('Current product price: ', productPrice);

    // 找到第一个满足条件的折扣梯度
    // 注意：minThreshold是以分为单位，需要将商品价格转换为分进行比较
    const productPriceInCents = productPrice * 100; // 将商品价格转换为分
    console.log('Product price in cents: ', productPriceInCents);

    for (const tier of sortedTiers) {
      console.log(`Checking tier: ${tier.minThresholdText}円 (${tier.minThreshold} cents)`);
      if (productPriceInCents >= tier.minThreshold) {
        const discountPercent = 100 - tier.discount;
        if (discountPercent > bestApiDiscount) {
          bestApiDiscount = discountPercent;
          appliedTier = tier;
          appliedDiscounts = [{
            activityName: discount.activityName,
            discountPercent: discountPercent,
            discountText: tier.discountText,
            minThreshold: tier.minThreshold,
            minThresholdText: tier.minThresholdText
          }];
        }
        break; // 找到满足条件的梯度后就不再继续查找
      }
    }
  });

  // 正确的叠加折扣逻辑：
  // 1. 在当前售价（已折扣价）基础上应用API折扣
  // 2. 计算相对于绝对原价的总折扣率
  let finalSalePrice = productInfo.salePrice;
  console.log('productInfo: ', productInfo);
  console.log('appliedTier: ', appliedTier);

  let hasApiDiscount = false;

  if (bestApiDiscount > 0 && appliedTier) {
    // 在当前售价（已折扣价）基础上应用API折扣
    // 例如：当前售价 $90，API折扣 20%，新售价 = $90 * (1 - 0.20) = $72
    // 使用Math.floor而不是Math.round，确保小数部分向下取整
    finalSalePrice = Math.floor(productInfo.salePrice * (100 - bestApiDiscount) / 100);
    hasApiDiscount = true;

    console.log(`API discount applied: ${productInfo.salePrice} * (1 - ${bestApiDiscount}%) = ${finalSalePrice} (min threshold: ${appliedTier.minThresholdText}円)`);
  } else {
    console.log(`No applicable discount for product price ${productInfo.salePrice} (${formatPrice(productInfo.salePrice)})`);
  }

  // 计算相对于绝对原价的总折扣率
  // 例如：原价 $100，当前售价 $90，叠加折扣后售价 $72，总折扣率 = (100 - 72) / 100 = 28%
  let finalDiscount = 0;
  if (productInfo.originalPrice > 0 && finalSalePrice < productInfo.originalPrice) {
    finalDiscount = Math.round((productInfo.originalPrice - finalSalePrice) / productInfo.originalPrice * 100);
  }

  return {
    finalSalePrice,
    finalOriginalPrice: productInfo.originalPrice,
    finalDiscount,
    appliedDiscounts,
    hasApiDiscount,
    // 调试信息
    debugInfo: {
      originalPrice: productInfo.originalPrice,
      currentSalePrice: productInfo.salePrice,
      currentDiscount: productInfo.currentDiscount,
      apiDiscountPercent: bestApiDiscount,
      appliedThreshold: appliedTier ? appliedTier.minThreshold : null,
      finalSalePrice,
      totalDiscountPercent: finalDiscount
    }
  };
}

/**
 * 格式化价格显示
 * @param {number} price - 价格
 * @returns {string} 格式化后的价格字符串
 */
function formatPrice(price) {
  return new Intl.NumberFormat(CONFIG.CURRENCY.LOCALE, {
    style: 'currency',
    currency: 'JPY',
    minimumFractionDigits: 0
  }).format(price);
}

/**
 * 更新商品卡片的价格和折扣显示
 * @param {Object} productInfo - 商品信息
 * @param {Object} priceData - 计算后的价格数据
 */
function updateProductCardDisplay(productInfo, priceData) {
  const { domElements } = productInfo;

  try {
    // 更新售价
    if (domElements.salePriceElement) {
      const formattedSalePrice = formatPrice(priceData.finalSalePrice);
      // 保留原有的税费信息
      const taxInfo = domElements.salePriceElement.querySelector('p');
      domElements.salePriceElement.innerHTML = `${formattedSalePrice}${taxInfo ? taxInfo.outerHTML : ''} ~`;
    }

    // 更新原价
    if (domElements.regularPriceElement && priceData.finalSalePrice < priceData.finalOriginalPrice) {
      const formattedOriginalPrice = formatPrice(priceData.finalOriginalPrice);
      domElements.regularPriceElement.textContent = formattedOriginalPrice;
    }

    // 更新折扣徽章
    if (domElements.badgeElement && priceData.finalDiscount > 0) {
      const badgeSpan = domElements.badgeElement.querySelector('span');
      if (badgeSpan) {
        badgeSpan.textContent = `${priceData.finalDiscount}％OFF`;
      }
    }
    productInfo.domElements.productCard?.setAttribute('data-discount-updated', '1');

    console.log(`Updated product ${productInfo.productId}: ${formatPrice(priceData.finalSalePrice)} (${priceData.finalDiscount}% OFF)`);
  } catch (error) {
    console.error('Error updating product card display:', error);
  }
}

/**
 * 处理单个商品的折扣更新（改良版）
 * 兼容：
 *  1. 轮播 / 无限滚动克隆出的多份相同商品卡
 *  2. 首次已算过折扣后，后续出现的同款卡片复用缓存而不重复计算
 *
 * @param {Element} productCard  当前捕获到的商品卡片节点
 */
async function processProductDiscount(productCard) {
  try {
    /* ---------- 解析本张卡片 ---------- */
    const productInfo = parseProductInfoFromDOM(productCard);
    if (!productInfo) return;

    /* ---------- 查缓存 ---------- */
    const cached = productInfoMap.get(productInfo.productId);

    /* ========== 分支 A：已有缓存 & 已算过 API 折扣 ========== */
    if (cached && cached.hasApiDiscount) {
      // 如果这张卡片还没更新过（用 data-discount-updated 标记）
      if (!productCard.hasAttribute('data-discount-updated')) {
        // 直接把缓存结果写入 UI（无需再次计算）
        const priceData = {
          finalSalePrice: cached.finalSalePrice,
          finalOriginalPrice: cached.finalOriginalPrice,
          finalDiscount: cached.finalDiscount,
          appliedDiscounts: cached.appliedDiscounts,
          hasApiDiscount: true
        };

        const domElements = {
          productCard,
          priceContainer: productCard.querySelector(CONFIG.SELECTORS.PRICE_CONTAINER),
          badgeElement: productCard.querySelector(CONFIG.SELECTORS.BADGE),
          salePriceElement: productCard.querySelector(CONFIG.SELECTORS.PRICE_SALE),
          regularPriceElement: productCard.querySelector(CONFIG.SELECTORS.PRICE_REGULAR)
        };

        updateProductCardDisplay({ ...productInfo, domElements }, priceData);
        productCard.setAttribute('data-discount-updated', '1');
      }
      return;   // 不再进行昂贵的计算
    }

    /* ========== 分支 B：首次或需要重新计算 ========== */
    console.log(`Processing product ${productInfo.productId}…`);

    // 使用全局折扣数据
    const priceData = calculateDiscountedPrices(productInfo, globalDiscountData);

    // 更新到 **所有** 同款卡片（轮播可能有多份副本）
    document
      .querySelectorAll(`[data-product-id="${productInfo.productId}"]:not([data-discount-updated])`)
      .forEach(card => {
        const domElements = {
          productCard: card,
          priceContainer: card.querySelector(CONFIG.SELECTORS.PRICE_CONTAINER),
          badgeElement: card.querySelector(CONFIG.SELECTORS.BADGE),
          salePriceElement: card.querySelector(CONFIG.SELECTORS.PRICE_SALE),
          regularPriceElement: card.querySelector(CONFIG.SELECTORS.PRICE_REGULAR)
        };
        updateProductCardDisplay({ ...productInfo, domElements }, priceData);
        card.setAttribute('data-discount-updated', '1');
      });

    /* ---------- 写缓存供后续同款卡片复用 ---------- */
    productInfoMap.set(productInfo.productId, {
      ...productInfo,
      ...priceData,
      lastUpdated: Date.now()
    });

    console.log(
      `Applied ${priceData.hasApiDiscount ? 'API' : 'existing'} discount to product ${productInfo.productId}`
    );
  } catch (err) {
    console.error('Error processing product discount:', err);
  }
}

/**
 * 查找包含data-product-id属性的祖先元素
 * @param {Element} element - 起始元素
 * @returns {Element|null} 找到的商品卡片元素
 */
function findProductCard(element) {
  let current = element;
  while (current && current !== document.body) {
    if (current.hasAttribute && current.hasAttribute('data-product-id')) {
      return current;
    }
    current = current.parentElement;
  }
  return null;
}

/**
 * 处理card__badge元素的变化
 * @param {Element} badgeElement - 折扣徽章元素
 */
function handleBadgeChange(badgeElement) {
  const productCard = findProductCard(badgeElement);
  if (productCard) {
    console.log('Badge change detected for product:', productCard.getAttribute('data-product-id'));
    processProductDiscount(productCard);
  }
}

/**
 * MutationObserver回调函数
 * @param {MutationRecord[]} mutations - 变化记录数组
 */
function handleMutations(mutations) {
  const processedCards = new Set();

  mutations.forEach(mutation => {
    // 处理新增的节点
    if (mutation.type === 'childList') {
      mutation.addedNodes.forEach(node => {
        if (node.nodeType === Node.ELEMENT_NODE) {
          // 检查新增的节点是否是card__badge
          if (node.classList && node.classList.contains('card__badge')) {
            handleBadgeChange(node);
          }

          // 检查新增节点的子元素中是否包含card__badge
          const badgeElements = node.querySelectorAll && node.querySelectorAll(CONFIG.SELECTORS.BADGE);
          if (badgeElements) {
            badgeElements.forEach(badge => {
              const productCard = findProductCard(badge);
              if (productCard && !processedCards.has(productCard)) {
                processedCards.add(productCard);
                handleBadgeChange(badge);
              }
            });
          }
        }
      });
    }

    // 处理内容变化
    if (mutation.type === 'characterData' || mutation.type === 'childList') {
      const target = mutation.target;

      // 检查变化的节点是否在card__badge内
      let badgeElement = null;
      if (target.classList && target.classList.contains('card__badge')) {
        badgeElement = target;
      } else {
        badgeElement = target.closest && target.closest(CONFIG.SELECTORS.BADGE);
      }

      if (badgeElement) {
        const productCard = findProductCard(badgeElement);
        if (productCard && !processedCards.has(productCard)) {
          processedCards.add(productCard);
          handleBadgeChange(badgeElement);
        }
      }
    }
  });
}

/**
 * 初始化MutationObserver
 */
function initializeMutationObserver() {
  // 创建MutationObserver实例
  const observer = new MutationObserver(handleMutations);

  // 配置观察选项
  const config = {
    childList: true,        // 观察子节点的变化
    subtree: true,          // 观察所有后代节点
    characterData: true,    // 观察文本内容变化
    attributes: false       // 不观察属性变化
  };

  // 开始观察
  observer.observe(document.body, config);

  console.log('MutationObserver initialized for card__badge elements');
  return observer;
}

/**
 * 处理页面上现有的商品卡片
 */
function processExistingProducts() {
  const productCards = document.querySelectorAll(CONFIG.SELECTORS.PRODUCT_CARD);
  console.log(`Found ${productCards.length} existing product cards`);

  productCards.forEach(productCard => {
    const badge = productCard.querySelector(CONFIG.SELECTORS.BADGE);
    if (badge) {
      processProductDiscount(productCard);
    }
  });
}

/**
 * 初始化折扣系统
 */
async function initializeDiscountSystem() {
  console.log('Initializing discount system...');

  // 在初始化阶段获取全局折扣信息
  const testProductId = '16069927806303341599314095';
  console.log('Fetching global discount data with test product ID:', testProductId);
  globalDiscountData = await getBasicProductDiscounts(testProductId);
  console.log('Global discount data fetched:', globalDiscountData);

  // 处理现有商品
  processExistingProducts();

  // 初始化MutationObserver
  const observer = initializeMutationObserver();

  // 返回observer以便后续可以停止观察
  return observer;
}

// 全局变量存储observer
let discountObserver = null;

/**
 * 停止折扣系统
 */
function stopDiscountSystem() {
  if (discountObserver) {
    discountObserver.disconnect();
    discountObserver = null;
    console.log('Discount system stopped');
  }
}

/**
 * 重启折扣系统
 */
async function restartDiscountSystem() {
  stopDiscountSystem();
  discountObserver = await initializeDiscountSystem();
}

/**
 * 清除所有已处理的商品信息
 */
function clearProductCache() {
  productInfoMap.clear();
  console.log('Product cache cleared');
}

/**
 * 强制重新处理所有商品（清除缓存后重新处理）
 */
function forceReprocessAllProducts() {
  clearProductCache();
  processExistingProducts();
  console.log('All products reprocessed');
}

// 页面加载完成后自动初始化
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', async () => {
    discountObserver = await initializeDiscountSystem();
  });
} else {
  // 如果页面已经加载完成，立即初始化
  (async () => {
    discountObserver = await initializeDiscountSystem();
  })();
}

// 暴露全局方法供调试使用
window.DiscountSystem = {
  init: async () => await initializeDiscountSystem(),
  stop: stopDiscountSystem,
  restart: restartDiscountSystem,
  processExisting: processExistingProducts,
  clearCache: clearProductCache,
  forceReprocess: forceReprocessAllProducts,
  productMap: productInfoMap,
  config: CONFIG,

  // 调试方法
  getProductInfo: (productId) => productInfoMap.get(productId),
  getAllProducts: () => Array.from(productInfoMap.entries()),
  getStats: () => ({
    totalProducts: productInfoMap.size,
    processedWithApiDiscount: Array.from(productInfoMap.values()).filter(p => p.hasApiDiscount).length
  })
};

console.log('Discount system loaded. Use window.DiscountSystem for debugging.');